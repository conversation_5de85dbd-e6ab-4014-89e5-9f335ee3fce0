import { 
  TestCategory, 
  UpcomingTest, 
  TestSyllabus, 
  TestMistake, 
  TestTakeaway,
  MockTest 
} from '@/types/mockTest';

// Local storage keys
const STORAGE_KEYS = {
  CATEGORIES: 'mocktest-categories',
  UPCOMING_TESTS: 'mocktest-upcoming',
  SYLLABUS: 'mocktest-syllabus',
  ENHANCED_DATA: 'mocktest-enhanced-data', // For storing enhanced fields of existing tests
  MISTAKES: 'mocktest-mistakes',
  TAKEAWAYS: 'mocktest-takeaways',
} as const;

// Generic localStorage utility functions
function getFromStorage<T>(key: string, defaultValue: T): T {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error(`Error reading from localStorage key "${key}":`, error);
    return defaultValue;
  }
}

function saveToStorage<T>(key: string, data: T): void {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error(`Error saving to localStorage key "${key}":`, error);
  }
}

// Test Categories Management
export const categoryStorage = {
  getAll: (userId: string): TestCategory[] => {
    const categories = getFromStorage<TestCategory[]>(STORAGE_KEYS.CATEGORIES, []);
    return categories.filter(cat => cat.userId === userId);
  },

  create: (category: Omit<TestCategory, 'id' | 'createdAt'>): TestCategory => {
    const categories = getFromStorage<TestCategory[]>(STORAGE_KEYS.CATEGORIES, []);
    const newCategory: TestCategory = {
      ...category,
      id: `cat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
    };
    categories.push(newCategory);
    saveToStorage(STORAGE_KEYS.CATEGORIES, categories);
    return newCategory;
  },

  update: (id: string, updates: Partial<TestCategory>): TestCategory | null => {
    const categories = getFromStorage<TestCategory[]>(STORAGE_KEYS.CATEGORIES, []);
    const index = categories.findIndex(cat => cat.id === id);
    if (index === -1) return null;
    
    categories[index] = { ...categories[index], ...updates };
    saveToStorage(STORAGE_KEYS.CATEGORIES, categories);
    return categories[index];
  },

  delete: (id: string): boolean => {
    const categories = getFromStorage<TestCategory[]>(STORAGE_KEYS.CATEGORIES, []);
    const filteredCategories = categories.filter(cat => cat.id !== id);
    if (filteredCategories.length === categories.length) return false;
    
    saveToStorage(STORAGE_KEYS.CATEGORIES, filteredCategories);
    return true;
  },

  getById: (id: string): TestCategory | null => {
    const categories = getFromStorage<TestCategory[]>(STORAGE_KEYS.CATEGORIES, []);
    return categories.find(cat => cat.id === id) || null;
  }
};

// Upcoming Tests Management
export const upcomingTestStorage = {
  getAll: (userId: string): UpcomingTest[] => {
    const tests = getFromStorage<UpcomingTest[]>(STORAGE_KEYS.UPCOMING_TESTS, []);
    return tests.filter(test => test.userId === userId);
  },

  create: (test: Omit<UpcomingTest, 'id' | 'createdAt' | 'daysLeft'>): UpcomingTest => {
    const tests = getFromStorage<UpcomingTest[]>(STORAGE_KEYS.UPCOMING_TESTS, []);
    const newTest: UpcomingTest = {
      ...test,
      id: `upcoming-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      daysLeft: calculateDaysLeft(test.date),
    };
    tests.push(newTest);
    saveToStorage(STORAGE_KEYS.UPCOMING_TESTS, tests);
    return newTest;
  },

  update: (id: string, updates: Partial<UpcomingTest>): UpcomingTest | null => {
    const tests = getFromStorage<UpcomingTest[]>(STORAGE_KEYS.UPCOMING_TESTS, []);
    const index = tests.findIndex(test => test.id === id);
    if (index === -1) return null;
    
    const updatedTest = { ...tests[index], ...updates };
    if (updates.date) {
      updatedTest.daysLeft = calculateDaysLeft(updates.date);
    }
    
    tests[index] = updatedTest;
    saveToStorage(STORAGE_KEYS.UPCOMING_TESTS, tests);
    return updatedTest;
  },

  delete: (id: string): boolean => {
    const tests = getFromStorage<UpcomingTest[]>(STORAGE_KEYS.UPCOMING_TESTS, []);
    const filteredTests = tests.filter(test => test.id !== id);
    if (filteredTests.length === tests.length) return false;
    
    saveToStorage(STORAGE_KEYS.UPCOMING_TESTS, filteredTests);
    return true;
  },

  getUpcoming: (userId: string, daysAhead: number = 30): UpcomingTest[] => {
    const tests = upcomingTestStorage.getAll(userId);
    const today = new Date();
    const futureDate = new Date(today.getTime() + (daysAhead * 24 * 60 * 60 * 1000));
    
    return tests
      .filter(test => {
        const testDate = new Date(test.date);
        return testDate >= today && testDate <= futureDate;
      })
      .map(test => ({
        ...test,
        daysLeft: calculateDaysLeft(test.date)
      }))
      .sort((a, b) => (a.daysLeft || 0) - (b.daysLeft || 0));
  }
};

// Syllabus Management
export const syllabusStorage = {
  get: (testId: string): TestSyllabus | null => {
    const syllabi = getFromStorage<TestSyllabus[]>(STORAGE_KEYS.SYLLABUS, []);
    return syllabi.find(syl => syl.testId === testId) || null;
  },

  save: (syllabus: TestSyllabus): void => {
    const syllabi = getFromStorage<TestSyllabus[]>(STORAGE_KEYS.SYLLABUS, []);
    const index = syllabi.findIndex(syl => syl.testId === syllabus.testId);
    
    const updatedSyllabus = {
      ...syllabus,
      overallProgress: calculateSyllabusProgress(syllabus.chapters),
      lastUpdated: new Date().toISOString()
    };
    
    if (index === -1) {
      syllabi.push(updatedSyllabus);
    } else {
      syllabi[index] = updatedSyllabus;
    }
    
    saveToStorage(STORAGE_KEYS.SYLLABUS, syllabi);
  },

  delete: (testId: string): boolean => {
    const syllabi = getFromStorage<TestSyllabus[]>(STORAGE_KEYS.SYLLABUS, []);
    const filteredSyllabi = syllabi.filter(syl => syl.testId !== testId);
    if (filteredSyllabi.length === syllabi.length) return false;
    
    saveToStorage(STORAGE_KEYS.SYLLABUS, filteredSyllabi);
    return true;
  }
};

// Enhanced Mock Test Data Management (for storing enhanced fields)
export const enhancedTestStorage = {
  get: (testId: string): Partial<MockTest> | null => {
    const enhancedData = getFromStorage<Record<string, Partial<MockTest>>>(STORAGE_KEYS.ENHANCED_DATA, {});
    return enhancedData[testId] || null;
  },

  save: (testId: string, data: Partial<MockTest>): void => {
    const enhancedData = getFromStorage<Record<string, Partial<MockTest>>>(STORAGE_KEYS.ENHANCED_DATA, {});
    enhancedData[testId] = { ...enhancedData[testId], ...data };
    saveToStorage(STORAGE_KEYS.ENHANCED_DATA, enhancedData);
  },

  delete: (testId: string): boolean => {
    const enhancedData = getFromStorage<Record<string, Partial<MockTest>>>(STORAGE_KEYS.ENHANCED_DATA, {});
    if (!(testId in enhancedData)) return false;
    
    delete enhancedData[testId];
    saveToStorage(STORAGE_KEYS.ENHANCED_DATA, enhancedData);
    return true;
  },

  getAll: (): Record<string, Partial<MockTest>> => {
    return getFromStorage<Record<string, Partial<MockTest>>>(STORAGE_KEYS.ENHANCED_DATA, {});
  }
};

// Utility functions
function calculateDaysLeft(dateString: string): number {
  const testDate = new Date(dateString);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  testDate.setHours(0, 0, 0, 0);

  const diffTime = testDate.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

// URL validation and utilities
export const urlUtils = {
  isValidUrl: (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  formatUrl: (url: string): string => {
    if (!url) return '';
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return `https://${url}`;
    }
    return url;
  },

  getFileType: (url: string): string => {
    const extension = url.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf': return 'PDF';
      case 'doc':
      case 'docx': return 'Word';
      case 'jpg':
      case 'jpeg':
      case 'png': return 'Image';
      default: return 'File';
    }
  },

  getDomainName: (url: string): string => {
    try {
      const domain = new URL(url).hostname;
      return domain.replace('www.', '');
    } catch {
      return 'Unknown';
    }
  }
};

function calculateSyllabusProgress(chapters: any[]): number {
  if (chapters.length === 0) return 0;
  const completedChapters = chapters.filter(ch => ch.isCompleted).length;
  return Math.round((completedChapters / chapters.length) * 100);
}

// Mistakes and Takeaways Management
export const mistakesStorage = {
  getByTest: (testId: string): TestMistake[] => {
    const mistakes = getFromStorage<TestMistake[]>(STORAGE_KEYS.MISTAKES, []);
    return mistakes.filter(mistake => mistake.id.startsWith(testId));
  },

  add: (testId: string, mistake: Omit<TestMistake, 'id' | 'createdAt'>): TestMistake => {
    const mistakes = getFromStorage<TestMistake[]>(STORAGE_KEYS.MISTAKES, []);
    const newMistake: TestMistake = {
      ...mistake,
      id: `${testId}-mistake-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
    };
    mistakes.push(newMistake);
    saveToStorage(STORAGE_KEYS.MISTAKES, mistakes);
    return newMistake;
  },

  update: (id: string, updates: Partial<TestMistake>): TestMistake | null => {
    const mistakes = getFromStorage<TestMistake[]>(STORAGE_KEYS.MISTAKES, []);
    const index = mistakes.findIndex(mistake => mistake.id === id);
    if (index === -1) return null;
    
    mistakes[index] = { ...mistakes[index], ...updates };
    saveToStorage(STORAGE_KEYS.MISTAKES, mistakes);
    return mistakes[index];
  },

  delete: (id: string): boolean => {
    const mistakes = getFromStorage<TestMistake[]>(STORAGE_KEYS.MISTAKES, []);
    const filteredMistakes = mistakes.filter(mistake => mistake.id !== id);
    if (filteredMistakes.length === mistakes.length) return false;
    
    saveToStorage(STORAGE_KEYS.MISTAKES, filteredMistakes);
    return true;
  }
};

export const takeawaysStorage = {
  getByTest: (testId: string): TestTakeaway[] => {
    const takeaways = getFromStorage<TestTakeaway[]>(STORAGE_KEYS.TAKEAWAYS, []);
    return takeaways.filter(takeaway => takeaway.id.startsWith(testId));
  },

  add: (testId: string, takeaway: Omit<TestTakeaway, 'id' | 'createdAt'>): TestTakeaway => {
    const takeaways = getFromStorage<TestTakeaway[]>(STORAGE_KEYS.TAKEAWAYS, []);
    const newTakeaway: TestTakeaway = {
      ...takeaway,
      id: `${testId}-takeaway-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
    };
    takeaways.push(newTakeaway);
    saveToStorage(STORAGE_KEYS.TAKEAWAYS, takeaways);
    return newTakeaway;
  },

  update: (id: string, updates: Partial<TestTakeaway>): TestTakeaway | null => {
    const takeaways = getFromStorage<TestTakeaway[]>(STORAGE_KEYS.TAKEAWAYS, []);
    const index = takeaways.findIndex(takeaway => takeaway.id === id);
    if (index === -1) return null;

    takeaways[index] = { ...takeaways[index], ...updates };
    saveToStorage(STORAGE_KEYS.TAKEAWAYS, takeaways);
    return takeaways[index];
  },

  delete: (id: string): boolean => {
    const takeaways = getFromStorage<TestTakeaway[]>(STORAGE_KEYS.TAKEAWAYS, []);
    const filteredTakeaways = takeaways.filter(takeaway => takeaway.id !== id);
    if (filteredTakeaways.length === takeaways.length) return false;

    saveToStorage(STORAGE_KEYS.TAKEAWAYS, filteredTakeaways);
    return true;
  }
};

// Enhanced Mock Test Utilities - Now fully local storage based
export const enhancedMockTestUtils = {
  // Storage key for mock tests
  getStorageKey: (userId: string) => `mock_tests_${userId}`,

  // Get all mock tests for a user with enhanced data
  getAll: (userId: string): MockTest[] => {
    try {
      const data = localStorage.getItem(enhancedMockTestUtils.getStorageKey(userId));
      const basicTests: MockTest[] = data ? JSON.parse(data) : [];

      // Merge with enhanced local data
      return basicTests.map(test => enhancedMockTestUtils.mergeWithLocalData(test));
    } catch (error) {
      console.error('Error loading mock tests:', error);
      return [];
    }
  },

  // Merge basic MockTest with enhanced local data
  mergeWithLocalData: (basicTest: MockTest): MockTest => {
    const enhancedData = enhancedTestStorage.get(basicTest.id);
    const mistakes = mistakesStorage.getByTest(basicTest.id);
    const takeaways = takeawaysStorage.getByTest(basicTest.id);

    return {
      ...basicTest,
      // Set defaults for enhanced fields
      isReviewed: false,
      mistakes: [],
      takeaways: [],
      difficulty: 'medium' as const,
      // Override with local data if available
      ...enhancedData,
      // Always use fresh mistakes and takeaways from storage
      mistakes,
      takeaways,
    };
  },

  // Save a mock test to local storage
  save: (userId: string, mockTest: MockTest): MockTest => {
    const tests = enhancedMockTestUtils.getAll(userId);
    const existingIndex = tests.findIndex(t => t.id === mockTest.id);

    const testToSave = {
      ...mockTest,
      createdAt: mockTest.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    let updatedTests: MockTest[];
    if (existingIndex >= 0) {
      updatedTests = [...tests];
      updatedTests[existingIndex] = testToSave;
    } else {
      updatedTests = [...tests, testToSave];
    }

    // Save basic test data
    const basicTests = updatedTests.map(test => {
      const { mistakes, takeaways, ...basicTest } = test;
      return basicTest;
    });

    localStorage.setItem(enhancedMockTestUtils.getStorageKey(userId), JSON.stringify(basicTests));

    // Save enhanced fields separately
    enhancedMockTestUtils.saveEnhancedFields(mockTest.id, mockTest);

    return testToSave;
  },

  // Update a mock test
  update: (userId: string, testId: string, updates: Partial<MockTest>): MockTest | null => {
    const tests = enhancedMockTestUtils.getAll(userId);
    const testIndex = tests.findIndex(t => t.id === testId);

    if (testIndex === -1) return null;

    const updatedTest = {
      ...tests[testIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    const updatedTests = [...tests];
    updatedTests[testIndex] = updatedTest;

    // Save basic test data
    const basicTests = updatedTests.map(test => {
      const { mistakes, takeaways, ...basicTest } = test;
      return basicTest;
    });

    localStorage.setItem(enhancedMockTestUtils.getStorageKey(userId), JSON.stringify(basicTests));

    // Save enhanced fields separately
    enhancedMockTestUtils.saveEnhancedFields(testId, updatedTest);

    return updatedTest;
  },

  // Delete a mock test
  delete: (userId: string, testId: string): boolean => {
    const tests = enhancedMockTestUtils.getAll(userId);
    const filteredTests = tests.filter(t => t.id !== testId);

    if (filteredTests.length === tests.length) return false;

    // Save basic test data
    const basicTests = filteredTests.map(test => {
      const { mistakes, takeaways, ...basicTest } = test;
      return basicTest;
    });

    localStorage.setItem(enhancedMockTestUtils.getStorageKey(userId), JSON.stringify(basicTests));

    // Clean up enhanced data
    enhancedMockTestUtils.cleanupLocalData(testId);

    return true;
  },

  // Merge multiple tests with their enhanced data
  mergeMultipleWithLocalData: (basicTests: MockTest[]): MockTest[] => {
    return basicTests.map(test => enhancedMockTestUtils.mergeWithLocalData(test));
  },

  // Save enhanced fields to local storage
  saveEnhancedFields: (testId: string, enhancedFields: Partial<MockTest>): void => {
    // Extract only the enhanced fields that should be stored locally
    const {
      categoryId,
      testPaperUrl,
      isReviewed,
      reviewedAt,
      difficulty,
      timeSpent,
      targetScore,
      isFromUpcoming,
      // Don't store mistakes and takeaways here - they have their own storage
      mistakes,
      takeaways,
      ...otherFields
    } = enhancedFields;

    const dataToStore = {
      categoryId,
      testPaperUrl,
      isReviewed,
      reviewedAt,
      difficulty,
      timeSpent,
      targetScore,
      isFromUpcoming,
    };

    // Remove undefined values
    const cleanedData = Object.fromEntries(
      Object.entries(dataToStore).filter(([_, value]) => value !== undefined)
    );

    if (Object.keys(cleanedData).length > 0) {
      enhancedTestStorage.save(testId, cleanedData);
    }
  },

  // Mark test as reviewed
  markAsReviewed: (testId: string): void => {
    enhancedTestStorage.save(testId, {
      isReviewed: true,
      reviewedAt: new Date().toISOString(),
    });
  },

  // Mark test as unreviewed
  markAsUnreviewed: (testId: string): void => {
    enhancedTestStorage.save(testId, {
      isReviewed: false,
      reviewedAt: undefined,
    });
  },

  // Get all enhanced tests for a user
  getAllEnhancedTests: async (userId: string, getBasicTests: () => Promise<MockTest[]>): Promise<MockTest[]> => {
    const basicTests = await getBasicTests();
    return enhancedMockTestUtils.mergeMultipleWithLocalData(basicTests);
  },

  // Create a test from upcoming test
  createFromUpcoming: (upcomingTest: UpcomingTest): Partial<MockTest> => {
    return {
      name: upcomingTest.name,
      date: upcomingTest.date,
      categoryId: upcomingTest.categoryId,
      testPaperUrl: upcomingTest.testPaperUrl,
      isFromUpcoming: true,
      isReviewed: false,
      mistakes: [],
      takeaways: [],
      difficulty: 'medium' as const,
    };
  },

  // Clean up local data for deleted test
  cleanupLocalData: (testId: string): void => {
    enhancedTestStorage.delete(testId);
    syllabusStorage.delete(testId);

    // Clean up mistakes and takeaways
    const mistakes = mistakesStorage.getByTest(testId);
    const takeaways = takeawaysStorage.getByTest(testId);

    mistakes.forEach(mistake => mistakesStorage.delete(mistake.id));
    takeaways.forEach(takeaway => takeawaysStorage.delete(takeaway.id));
  }
};

// D-Day Exam Interface
export interface DDayExam {
  id: string;
  name: string;
  date: string; // YYYY-MM-DD format
  time: string; // HH:MM format
  userId: string;
  status?: 'upcoming' | 'completed' | 'missed';
  createdAt: string;
  updatedAt: string;
}

// D-Day Local Storage Utilities
export const dDayStorage = {
  // Storage key
  getStorageKey: (userId: string) => `dday_exams_${userId}`,

  // Get all D-Day exams for a user
  getAll: (userId: string): DDayExam[] => {
    try {
      const data = localStorage.getItem(dDayStorage.getStorageKey(userId));
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Error loading D-Day exams:', error);
      return [];
    }
  },

  // Get upcoming D-Day exams (within specified days)
  getUpcoming: (userId: string, daysAhead: number = 30): DDayExam[] => {
    const allExams = dDayStorage.getAll(userId);
    const now = new Date();
    const futureDate = new Date();
    futureDate.setDate(now.getDate() + daysAhead);

    return allExams.filter(exam => {
      const examDate = new Date(exam.date);
      return examDate >= now && examDate <= futureDate && exam.status !== 'completed';
    }).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  },

  // Get exam by ID
  getById: (userId: string, examId: string): DDayExam | null => {
    const exams = dDayStorage.getAll(userId);
    return exams.find(exam => exam.id === examId) || null;
  },

  // Save exam
  save: (userId: string, exam: DDayExam): DDayExam => {
    const exams = dDayStorage.getAll(userId);
    const existingIndex = exams.findIndex(e => e.id === exam.id);

    const examToSave = {
      ...exam,
      updatedAt: new Date().toISOString()
    };

    if (existingIndex >= 0) {
      exams[existingIndex] = examToSave;
    } else {
      exams.push(examToSave);
    }

    localStorage.setItem(dDayStorage.getStorageKey(userId), JSON.stringify(exams));
    return examToSave;
  },

  // Update exam
  update: (userId: string, examId: string, updates: Partial<DDayExam>): DDayExam | null => {
    const exams = dDayStorage.getAll(userId);
    const examIndex = exams.findIndex(e => e.id === examId);

    if (examIndex === -1) return null;

    const updatedExam = {
      ...exams[examIndex],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    exams[examIndex] = updatedExam;
    localStorage.setItem(dDayStorage.getStorageKey(userId), JSON.stringify(exams));
    return updatedExam;
  },

  // Delete exam
  delete: (userId: string, examId: string): boolean => {
    const exams = dDayStorage.getAll(userId);
    const filteredExams = exams.filter(e => e.id !== examId);

    if (filteredExams.length === exams.length) return false;

    localStorage.setItem(dDayStorage.getStorageKey(userId), JSON.stringify(filteredExams));
    return true;
  },

  // Clear all exams for user
  clear: (userId: string): void => {
    localStorage.removeItem(dDayStorage.getStorageKey(userId));
  }
};

// D-Day Integration Utilities
export const dDayIntegration = {
  // Convert upcoming test to D-Day exam format
  convertToExam: (upcomingTest: UpcomingTest): DDayExam => {
    return {
      id: upcomingTest.id,
      name: upcomingTest.name,
      date: upcomingTest.date,
      time: upcomingTest.time || '09:00',
      userId: upcomingTest.userId,
      status: 'upcoming',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  },

  // Get upcoming tests that should appear in D-Day
  getUpcomingForDDay: (userId: string, daysAhead: number = 30): DDayExam[] => {
    // Get from both upcoming tests and direct D-Day exams
    const upcomingTests = upcomingTestStorage.getUpcoming(userId, daysAhead);
    const dDayExams = dDayStorage.getUpcoming(userId, daysAhead);

    // Convert upcoming tests to D-Day format
    const convertedTests = upcomingTests.map(test => dDayIntegration.convertToExam(test));

    // Combine and deduplicate (D-Day exams take precedence)
    const allExams = [...dDayExams];
    convertedTests.forEach(convertedTest => {
      if (!allExams.find(exam => exam.id === convertedTest.id)) {
        allExams.push(convertedTest);
      }
    });

    return allExams.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  },

  // Sync upcoming test with D-Day exam (now stores locally)
  syncWithDDay: (upcomingTest: UpcomingTest): DDayExam => {
    const examData = dDayIntegration.convertToExam(upcomingTest);
    return dDayStorage.save(upcomingTest.userId, examData);
  },

  // Remove D-Day exam when upcoming test is deleted
  removeDDayExam: (userId: string, testId: string): boolean => {
    return dDayStorage.delete(userId, testId);
  }
};
